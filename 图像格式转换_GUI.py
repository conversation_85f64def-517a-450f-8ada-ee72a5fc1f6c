#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像格式转换工具 - GUI版本
提供图形界面的图像格式转换功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from pathlib import Path
from PIL import Image, ImageTk
import threading
from 图像格式转换 import ImageConverter, SUPPORTED_FORMATS

class ImageConverterGUI:
    """图像格式转换器GUI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("图像格式转换工具")
        self.root.geometry("800x600")
        
        self.converter = ImageConverter()
        self.input_files = []
        self.output_dir = ""
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 输入文件选择
        ttk.Label(main_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        input_frame.columnconfigure(0, weight=1)
        
        ttk.Button(input_frame, text="选择文件", 
                  command=self.select_input_files).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(input_frame, text="选择文件夹", 
                  command=self.select_input_folder).grid(row=0, column=1, padx=(10, 0))
        ttk.Button(input_frame, text="清空", 
                  command=self.clear_input_files).grid(row=0, column=2, padx=(10, 0))
        
        # 输入文件列表
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        self.file_listbox = tk.Listbox(list_frame, height=8)
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 输出设置
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(0, weight=1)
        
        self.output_var = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var)
        self.output_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(output_frame, text="浏览", 
                  command=self.select_output_dir).grid(row=0, column=1, padx=(10, 0))
        
        # 格式选择
        ttk.Label(main_frame, text="目标格式:").grid(row=3, column=0, sticky=tk.W, pady=5)
        
        self.format_var = tk.StringVar(value=".jpg")
        format_combo = ttk.Combobox(main_frame, textvariable=self.format_var, 
                                   values=list(SUPPORTED_FORMATS.keys()), 
                                   state="readonly", width=15)
        format_combo.grid(row=3, column=1, sticky=tk.W, pady=5)
        
        # 质量设置
        ttk.Label(main_frame, text="图像质量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        
        quality_frame = ttk.Frame(main_frame)
        quality_frame.grid(row=4, column=1, sticky=tk.W, pady=5)
        
        self.quality_var = tk.IntVar(value=95)
        quality_scale = ttk.Scale(quality_frame, from_=1, to=100, 
                                 variable=self.quality_var, orient=tk.HORIZONTAL, length=200)
        quality_scale.grid(row=0, column=0)
        
        self.quality_label = ttk.Label(quality_frame, text="95")
        self.quality_label.grid(row=0, column=1, padx=(10, 0))
        
        quality_scale.configure(command=self.update_quality_label)
        
        # 选项
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding="5")
        options_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.recursive_var = tk.BooleanVar()
        ttk.Checkbutton(options_frame, text="递归处理子目录", 
                       variable=self.recursive_var).grid(row=0, column=0, sticky=tk.W)
        
        self.optimize_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="优化文件大小", 
                       variable=self.optimize_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        # 转换按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=20)
        
        self.convert_button = ttk.Button(button_frame, text="开始转换", 
                                        command=self.start_conversion)
        self.convert_button.grid(row=0, column=0, padx=5)
        
        ttk.Button(button_frame, text="预览", 
                  command=self.preview_image).grid(row=0, column=1, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, 
                                           maximum=100, length=400)
        self.progress_bar.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 状态标签
        self.status_var = tk.StringVar(value="就绪")
        self.status_label = ttk.Label(main_frame, textvariable=self.status_var)
        self.status_label.grid(row=8, column=0, columnspan=3, pady=5)
        
        # 配置网格权重
        main_frame.rowconfigure(1, weight=1)
        
    def update_quality_label(self, value):
        """更新质量标签"""
        self.quality_label.config(text=str(int(float(value))))
        
    def select_input_files(self):
        """选择输入文件"""
        filetypes = [
            ("所有支持的图像", " ".join(f"*{ext}" for ext in SUPPORTED_FORMATS.keys())),
            ("JPEG文件", "*.jpg *.jpeg"),
            ("PNG文件", "*.png"),
            ("所有文件", "*.*")
        ]
        
        files = filedialog.askopenfilenames(
            title="选择图像文件",
            filetypes=filetypes
        )
        
        if files:
            self.input_files.extend(files)
            self.update_file_list()
            
    def select_input_folder(self):
        """选择输入文件夹"""
        folder = filedialog.askdirectory(title="选择包含图像的文件夹")
        
        if folder:
            # 获取文件夹中的所有支持的图像文件
            folder_path = Path(folder)
            pattern = "**/*" if self.recursive_var.get() else "*"
            
            for file_path in folder_path.glob(pattern):
                if file_path.is_file() and self.converter.is_supported_format(str(file_path)):
                    self.input_files.append(str(file_path))
            
            self.update_file_list()
            
    def clear_input_files(self):
        """清空输入文件列表"""
        self.input_files.clear()
        self.update_file_list()
        
    def update_file_list(self):
        """更新文件列表显示"""
        self.file_listbox.delete(0, tk.END)
        for file_path in self.input_files:
            self.file_listbox.insert(tk.END, os.path.basename(file_path))
            
    def select_output_dir(self):
        """选择输出目录"""
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_var.set(directory)
            self.output_dir = directory
            
    def preview_image(self):
        """预览选中的图像"""
        selection = self.file_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个图像文件")
            return
            
        file_path = self.input_files[selection[0]]
        
        try:
            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title(f"预览 - {os.path.basename(file_path)}")
            preview_window.geometry("600x500")
            
            # 加载并显示图像
            with Image.open(file_path) as img:
                # 调整图像大小以适应窗口
                img.thumbnail((500, 400), Image.Resampling.LANCZOS)
                photo = ImageTk.PhotoImage(img)
                
                label = ttk.Label(preview_window, image=photo)
                label.image = photo  # 保持引用
                label.pack(pady=10)
                
                # 显示图像信息
                info = self.converter.get_image_info(file_path)
                if info:
                    info_text = f"格式: {info['format']} | 尺寸: {info['width']}x{info['height']} | 大小: {info['file_size']:,} 字节"
                    ttk.Label(preview_window, text=info_text).pack(pady=5)
                    
        except Exception as e:
            messagebox.showerror("错误", f"无法预览图像: {str(e)}")
            
    def start_conversion(self):
        """开始转换"""
        if not self.input_files:
            messagebox.showwarning("警告", "请先选择输入文件")
            return
            
        if not self.output_dir:
            messagebox.showwarning("警告", "请选择输出目录")
            return
            
        # 在新线程中执行转换
        self.convert_button.config(state="disabled")
        self.status_var.set("转换中...")
        
        thread = threading.Thread(target=self.convert_images)
        thread.daemon = True
        thread.start()
        
    def convert_images(self):
        """转换图像（在后台线程中执行）"""
        try:
            total_files = len(self.input_files)
            success_count = 0
            failed_count = 0
            
            for i, input_file in enumerate(self.input_files):
                # 更新进度
                progress = (i / total_files) * 100
                self.progress_var.set(progress)
                self.status_var.set(f"转换中... ({i+1}/{total_files})")
                
                # 构建输出文件路径
                input_path = Path(input_file)
                output_file = Path(self.output_dir) / input_path.with_suffix(self.format_var.get()).name
                
                # 转换图像
                success = self.converter.convert_single_image(
                    input_file,
                    str(output_file),
                    quality=self.quality_var.get(),
                    optimize=self.optimize_var.get()
                )
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
            
            # 完成
            self.progress_var.set(100)
            self.status_var.set(f"转换完成! 成功: {success_count}, 失败: {failed_count}")
            
            # 显示结果
            self.root.after(0, lambda: messagebox.showinfo(
                "转换完成", 
                f"转换完成!\n成功: {success_count} 个文件\n失败: {failed_count} 个文件"
            ))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"转换过程中出现错误: {str(e)}"))
        
        finally:
            # 重新启用按钮
            self.root.after(0, lambda: self.convert_button.config(state="normal"))


def main():
    """主函数"""
    root = tk.Tk()
    app = ImageConverterGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
