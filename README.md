# 图像格式转换工具

一个功能强大的图像格式转换工具，支持多种常见图像格式之间的相互转换。提供命令行和图形界面两种使用方式。

## 支持的格式

- JPEG/JPG
- PNG
- BMP
- TIFF/TIF
- GIF
- WEBP
- ICO

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 命令行版本

#### 基本用法

```bash
# 转换单个文件
python 图像格式转换.py -i input.jpg -o output.png

# 批量转换目录中的所有图像为JPEG格式
python 图像格式转换.py -d input_dir -od output_dir -f .jpg

# 递归转换子目录，设置质量为90
python 图像格式转换.py -d input_dir -od output_dir -f .png -q 90 -r

# 查看图像信息
python 图像格式转换.py -i image.jpg --info

# 列出支持的格式
python 图像格式转换.py --list-formats
```

#### 命令行参数

- `-i, --input`: 输入图像文件路径
- `-d, --input-dir`: 输入目录路径
- `-o, --output`: 输出图像文件路径
- `-od, --output-dir`: 输出目录路径
- `-f, --format`: 目标格式 (如: .jpg, .png, .webp)
- `-q, --quality`: 图像质量 (1-100, 默认: 95)
- `-r, --recursive`: 递归处理子目录
- `--no-optimize`: 禁用文件大小优化
- `--info`: 显示图像信息
- `--list-formats`: 列出支持的格式

### 2. 图形界面版本

```bash
python 图像格式转换_GUI.py
```

图形界面提供以下功能：

- **文件选择**: 可以选择单个或多个文件，也可以选择整个文件夹
- **格式转换**: 支持所有常见图像格式之间的转换
- **质量控制**: 可调节输出图像的质量（1-100）
- **批量处理**: 支持批量转换多个文件
- **进度显示**: 实时显示转换进度
- **图像预览**: 可以预览选中的图像文件
- **递归处理**: 可以递归处理子目录中的图像

## 功能特点

### 智能格式处理

- **透明度处理**: 自动处理RGBA图像转换为不支持透明度的格式（如JPEG）
- **颜色模式转换**: 智能处理不同颜色模式之间的转换
- **质量优化**: 支持JPEG、WEBP等格式的质量调节
- **文件大小优化**: 自动优化输出文件大小

### 批量处理

- **目录扫描**: 自动扫描目录中的所有支持格式图像
- **递归处理**: 可选择是否处理子目录
- **进度跟踪**: 实时显示处理进度和结果统计
- **错误处理**: 详细的错误日志和异常处理

### 图像信息

- **格式检测**: 自动检测图像格式和属性
- **尺寸信息**: 显示图像宽度、高度和文件大小
- **颜色模式**: 显示图像的颜色模式（RGB、RGBA、P等）

## 使用示例

### 示例1: 将所有PNG图像转换为JPEG

```bash
python 图像格式转换.py -d ./images -od ./converted -f .jpg -q 85
```

### 示例2: 递归转换所有图像为WEBP格式

```bash
python 图像格式转换.py -d ./photos -od ./webp_photos -f .webp -q 90 -r
```

### 示例3: 查看图像详细信息

```bash
python 图像格式转换.py -i photo.jpg --info
```

输出示例：
```
图像信息: photo.jpg
  格式: JPEG
  模式: RGB
  尺寸: 1920 x 1080
  文件大小: 245,760 字节
```

## 注意事项

1. **透明度**: 转换为JPEG或BMP格式时，透明背景会被替换为白色背景
2. **质量设置**: 质量参数仅对JPEG和WEBP格式有效
3. **文件覆盖**: 如果输出文件已存在，会被覆盖
4. **内存使用**: 处理大图像时可能需要较多内存
5. **格式限制**: 某些特殊的图像格式可能不被支持

## 错误处理

工具包含完善的错误处理机制：

- 文件不存在或无法读取
- 不支持的图像格式
- 磁盘空间不足
- 权限问题
- 图像损坏或格式错误

所有错误都会记录到日志中，并显示详细的错误信息。

## 性能优化

- 使用Pillow库的高效图像处理算法
- 支持多线程处理（GUI版本）
- 内存优化，及时释放图像资源
- 智能的文件大小优化选项

## 许可证

本项目采用MIT许可证。
