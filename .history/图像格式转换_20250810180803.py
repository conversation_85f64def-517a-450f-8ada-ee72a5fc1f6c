#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像格式转换工具
支持多种图像格式之间的相互转换，包括：
- JPEG/JPG
- PNG
- BMP
- TIFF/TIF
- GIF
- WEBP
- ICO
"""

import os
import sys
import argparse
from pathlib import Path
from typing import List, Optional, Tuple
from PIL import Image, ImageSequence
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 支持的图像格式
SUPPORTED_FORMATS = {
    '.jpg': 'JPEG',
    '.jpeg': 'JPEG',
    '.png': 'PNG',
    '.bmp': 'BMP',
    '.tiff': 'TIFF',
    '.tif': 'TIFF',
    '.gif': 'GIF',
    '.webp': 'WEBP',
    '.ico': 'ICO'
}

class ImageConverter:
    """图像格式转换器"""

    def __init__(self):
        self.converted_count = 0
        self.failed_count = 0

    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return list(SUPPORTED_FORMATS.keys())

    def is_supported_format(self, file_path: str) -> bool:
        """检查文件格式是否支持"""
        ext = Path(file_path).suffix.lower()
        return ext in SUPPORTED_FORMATS

    def convert_single_image(self, input_path: str, output_path: str,
                           quality: int = 95, optimize: bool = True) -> bool:
        """
        转换单个图像文件

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            quality: JPEG质量 (1-100)
            optimize: 是否优化文件大小

        Returns:
            bool: 转换是否成功
        """
        try:
            # 检查输入文件是否存在
            if not os.path.exists(input_path):
                logger.error(f"输入文件不存在: {input_path}")
                return False

            # 检查输入格式是否支持
            if not self.is_supported_format(input_path):
                logger.error(f"不支持的输入格式: {input_path}")
                return False

            # 获取输出格式
            output_ext = Path(output_path).suffix.lower()
            if output_ext not in SUPPORTED_FORMATS:
                logger.error(f"不支持的输出格式: {output_ext}")
                return False

            output_format = SUPPORTED_FORMATS[output_ext]

            # 创建输出目录
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            # 打开并转换图像
            with Image.open(input_path) as img:
                # 处理透明度
                if output_format in ['JPEG', 'BMP'] and img.mode in ['RGBA', 'LA']:
                    # JPEG和BMP不支持透明度，转换为RGB
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img)
                    img = background

                # 处理调色板模式
                if img.mode == 'P' and output_format not in ['GIF', 'PNG']:
                    img = img.convert('RGB')

                # 保存参数
                save_kwargs = {'format': output_format, 'optimize': optimize}

                # 根据格式设置特定参数
                if output_format == 'JPEG':
                    save_kwargs['quality'] = quality
                    save_kwargs['progressive'] = True
                elif output_format == 'PNG':
                    save_kwargs['compress_level'] = 6
                elif output_format == 'WEBP':
                    save_kwargs['quality'] = quality
                    save_kwargs['method'] = 6

                # 保存图像
                img.save(output_path, **save_kwargs)

            logger.info(f"转换成功: {input_path} -> {output_path}")
            self.converted_count += 1
            return True

        except Exception as e:
            logger.error(f"转换失败 {input_path}: {str(e)}")
            self.failed_count += 1
            return False

    def convert_batch(self, input_dir: str, output_dir: str,
                     target_format: str, quality: int = 95,
                     recursive: bool = False) -> Tuple[int, int]:
        """
        批量转换图像

        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            target_format: 目标格式 (如 '.jpg', '.png')
            quality: 图像质量
            recursive: 是否递归处理子目录

        Returns:
            Tuple[int, int]: (成功数量, 失败数量)
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)

        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return 0, 0

        # 确保目标格式有效
        if target_format not in SUPPORTED_FORMATS:
            logger.error(f"不支持的目标格式: {target_format}")
            return 0, 0

        # 重置计数器
        self.converted_count = 0
        self.failed_count = 0

        # 获取所有图像文件
        pattern = "**/*" if recursive else "*"
        for file_path in input_path.glob(pattern):
            if file_path.is_file() and self.is_supported_format(str(file_path)):
                # 构建输出文件路径
                relative_path = file_path.relative_to(input_path)
                output_file = output_path / relative_path.with_suffix(target_format)

                # 转换图像
                self.convert_single_image(str(file_path), str(output_file), quality)

        return self.converted_count, self.failed_count

    def get_image_info(self, file_path: str) -> Optional[dict]:
        """
        获取图像信息

        Args:
            file_path: 图像文件路径

        Returns:
            dict: 图像信息字典，包含格式、尺寸、模式等
        """
        try:
            with Image.open(file_path) as img:
                info = {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'file_size': os.path.getsize(file_path)
                }
                return info
        except Exception as e:
            logger.error(f"获取图像信息失败 {file_path}: {str(e)}")
            return None


def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='图像格式转换工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 转换单个文件
  python 图像格式转换.py -i input.jpg -o output.png

  # 批量转换目录中的所有图像为JPEG格式
  python 图像格式转换.py -d input_dir -od output_dir -f .jpg

  # 递归转换子目录，设置质量为90
  python 图像格式转换.py -d input_dir -od output_dir -f .png -q 90 -r

  # 查看图像信息
  python 图像格式转换.py -i image.jpg --info

  # 列出支持的格式
  python 图像格式转换.py --list-formats
        """
    )

    # 输入选项
    input_group = parser.add_mutually_exclusive_group(required=False)
    input_group.add_argument('-i', '--input', help='输入图像文件路径')
    input_group.add_argument('-d', '--input-dir', help='输入目录路径')

    # 输出选项
    parser.add_argument('-o', '--output', help='输出图像文件路径')
    parser.add_argument('-od', '--output-dir', help='输出目录路径')
    parser.add_argument('-f', '--format', help='目标格式 (如: .jpg, .png, .webp)')

    # 转换选项
    parser.add_argument('-q', '--quality', type=int, default=95,
                       help='图像质量 (1-100, 默认: 95)')
    parser.add_argument('-r', '--recursive', action='store_true',
                       help='递归处理子目录')
    parser.add_argument('--no-optimize', action='store_true',
                       help='禁用文件大小优化')

    # 信息选项
    parser.add_argument('--info', action='store_true',
                       help='显示图像信息')
    parser.add_argument('--list-formats', action='store_true',
                       help='列出支持的格式')

    return parser


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    converter = ImageConverter()

    # 列出支持的格式
    if args.list_formats:
        print("支持的图像格式:")
        for ext, fmt in SUPPORTED_FORMATS.items():
            print(f"  {ext} ({fmt})")
        return

    # 显示图像信息
    if args.info:
        if not args.input:
            print("错误: 需要指定输入文件 (-i)")
            return

        info = converter.get_image_info(args.input)
        if info:
            print(f"图像信息: {args.input}")
            print(f"  格式: {info['format']}")
            print(f"  模式: {info['mode']}")
            print(f"  尺寸: {info['width']} x {info['height']}")
            print(f"  文件大小: {info['file_size']:,} 字节")
        return

    # 转换单个文件
    if args.input:
        if not args.output:
            print("错误: 需要指定输出文件 (-o)")
            return

        success = converter.convert_single_image(
            args.input,
            args.output,
            quality=args.quality,
            optimize=not args.no_optimize
        )

        if success:
            print(f"转换完成: {args.input} -> {args.output}")
        else:
            print(f"转换失败: {args.input}")
            sys.exit(1)

    # 批量转换
    elif args.input_dir:
        if not args.output_dir:
            print("错误: 需要指定输出目录 (-od)")
            return

        if not args.format:
            print("错误: 需要指定目标格式 (-f)")
            return

        print(f"开始批量转换...")
        print(f"输入目录: {args.input_dir}")
        print(f"输出目录: {args.output_dir}")
        print(f"目标格式: {args.format}")
        print(f"质量设置: {args.quality}")
        print(f"递归处理: {'是' if args.recursive else '否'}")
        print("-" * 50)

        success_count, failed_count = converter.convert_batch(
            args.input_dir,
            args.output_dir,
            args.format,
            quality=args.quality,
            recursive=args.recursive
        )

        print("-" * 50)
        print(f"转换完成!")
        print(f"成功: {success_count} 个文件")
        print(f"失败: {failed_count} 个文件")

        if failed_count > 0:
            sys.exit(1)

    else:
        parser.print_help()


if __name__ == '__main__':
    main()