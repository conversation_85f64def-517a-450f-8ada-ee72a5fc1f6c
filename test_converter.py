#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像格式转换工具测试脚本
"""

import os
import tempfile
import shutil
from pathlib import Path
from PIL import Image
from 图像格式转换 import ImageConverter, SUPPORTED_FORMATS

def create_test_images(test_dir):
    """创建测试图像"""
    test_images = []
    
    # 创建不同格式的测试图像
    formats_to_create = ['.jpg', '.png', '.bmp']
    
    for i, fmt in enumerate(formats_to_create):
        # 创建一个简单的彩色图像
        img = Image.new('RGB', (100, 100), color=(255 * i // len(formats_to_create), 128, 255))
        
        # 添加一些图案
        for x in range(0, 100, 10):
            for y in range(0, 100, 10):
                if (x + y) % 20 == 0:
                    img.putpixel((x, y), (255, 255, 255))
        
        file_path = test_dir / f'test_image_{i}{fmt}'
        img.save(file_path)
        test_images.append(str(file_path))
        print(f"创建测试图像: {file_path}")
    
    # 创建一个带透明度的PNG图像
    img_rgba = Image.new('RGBA', (100, 100), color=(255, 0, 0, 128))
    rgba_path = test_dir / 'test_transparent.png'
    img_rgba.save(rgba_path)
    test_images.append(str(rgba_path))
    print(f"创建透明PNG图像: {rgba_path}")
    
    return test_images

def test_single_conversion():
    """测试单个文件转换"""
    print("\n=== 测试单个文件转换 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试图像
        test_images = create_test_images(temp_path)
        
        converter = ImageConverter()
        
        for input_file in test_images:
            input_path = Path(input_file)
            
            # 测试转换为不同格式
            for target_ext in ['.jpg', '.png', '.webp']:
                output_file = temp_path / f"converted_{input_path.stem}{target_ext}"
                
                print(f"转换: {input_path.name} -> {output_file.name}")
                
                success = converter.convert_single_image(
                    str(input_file), 
                    str(output_file),
                    quality=85
                )
                
                if success and output_file.exists():
                    print(f"  ✓ 成功 (大小: {output_file.stat().st_size} 字节)")
                    
                    # 验证转换后的图像
                    try:
                        with Image.open(output_file) as img:
                            print(f"  ✓ 验证成功 - 格式: {img.format}, 尺寸: {img.size}")
                    except Exception as e:
                        print(f"  ✗ 验证失败: {e}")
                else:
                    print(f"  ✗ 转换失败")

def test_batch_conversion():
    """测试批量转换"""
    print("\n=== 测试批量转换 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_dir = temp_path / "input"
        output_dir = temp_path / "output"
        
        input_dir.mkdir()
        output_dir.mkdir()
        
        # 创建测试图像
        test_images = create_test_images(input_dir)
        
        # 创建子目录和更多图像
        sub_dir = input_dir / "subdir"
        sub_dir.mkdir()
        
        # 在子目录中创建更多图像
        img = Image.new('RGB', (50, 50), color=(0, 255, 0))
        sub_image = sub_dir / "sub_test.png"
        img.save(sub_image)
        
        converter = ImageConverter()
        
        # 测试批量转换
        print(f"批量转换目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        
        success_count, failed_count = converter.convert_batch(
            str(input_dir),
            str(output_dir),
            '.webp',
            quality=80,
            recursive=True
        )
        
        print(f"批量转换结果: 成功 {success_count}, 失败 {failed_count}")
        
        # 检查输出文件
        output_files = list(output_dir.rglob("*.webp"))
        print(f"生成的文件数量: {len(output_files)}")
        
        for output_file in output_files:
            print(f"  生成文件: {output_file.relative_to(output_dir)}")

def test_image_info():
    """测试图像信息获取"""
    print("\n=== 测试图像信息获取 ===")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试图像
        test_images = create_test_images(temp_path)
        
        converter = ImageConverter()
        
        for image_file in test_images:
            print(f"\n图像文件: {Path(image_file).name}")
            
            info = converter.get_image_info(image_file)
            if info:
                print(f"  格式: {info['format']}")
                print(f"  模式: {info['mode']}")
                print(f"  尺寸: {info['width']} x {info['height']}")
                print(f"  文件大小: {info['file_size']:,} 字节")
            else:
                print("  ✗ 获取信息失败")

def test_format_support():
    """测试格式支持"""
    print("\n=== 测试格式支持 ===")
    
    converter = ImageConverter()
    
    print("支持的格式:")
    for ext, fmt in SUPPORTED_FORMATS.items():
        print(f"  {ext} -> {fmt}")
    
    # 测试格式检查
    test_files = [
        "test.jpg", "test.png", "test.gif", "test.bmp", 
        "test.webp", "test.tiff", "test.ico", "test.txt"
    ]
    
    print("\n格式支持检查:")
    for file_name in test_files:
        is_supported = converter.is_supported_format(file_name)
        status = "✓" if is_supported else "✗"
        print(f"  {status} {file_name}")

def main():
    """运行所有测试"""
    print("开始图像格式转换工具测试...")
    
    try:
        test_format_support()
        test_single_conversion()
        test_batch_conversion()
        test_image_info()
        
        print("\n=== 测试完成 ===")
        print("所有测试已完成！如果没有错误信息，说明工具运行正常。")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
